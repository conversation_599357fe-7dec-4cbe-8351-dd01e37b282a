// Global helpers
// ------------------------------

// Setting arrays
var appRoutes = new Map();
function addRoute(name, url) {
    appRoutes.set(name, url);
}

var appSettings = new Map();
function addSetting(name, value) {
    appSettings.set(name, value);
}

var pageVariables = new Map();
function addVariables(name, value) {
    pageVariables.set(name, value);
}

// User permissions map
var userPermissions = new Map();
function addUserPermission(permissionCode, permissionTypes) {
    userPermissions.set(permissionCode, permissionTypes.split('-_-'));
}

// Helper function to check if user has a specific permission
function hasPermission(permissionCode, permissionType) {
    if (!permissionCode || !permissionType) {
        return false;
    }
    if (userPermissions.get('GLOBAL') && userPermissions.get('GLOBAL').includes(permissionType.toLowerCase())) {
        return true;
    }

    const permissions = userPermissions.get(permissionCode);
    if (!permissions || !Array.isArray(permissions)) {
        return false;
    }

    return permissions.includes(permissionType.toLowerCase());
}

// Date format
moment.locale('it');

// Block UI
$.blockUI.defaults = {
    message: '<div class="blockui-loading">',
    css: {
        padding: 0,
        margin: 0,
        top: '45%',
        left: '50%',
        right: '50%',
        border: 'none',
        backgroundColor: 'transparent',
        cursor: 'wait'
    },
    overlayCSS: {
        backgroundColor: '#000',
        opacity: 0.4,
        cursor: 'wait'
    },
    baseZ: 1100,
    showOverlay: true
};

// Cancel button
document.addEventListener('DOMContentLoaded', (event) => {
    // forza Preline ad inizializzare tutte le collection prima
    HSStaticMethods.autoInit();

    if (document.querySelector('.btn-cancel') !== null) {
        document.querySelector('.btn-cancel').addEventListener('click', function (e) {
            e.preventDefault();
            var href = this.getAttribute('href');

            // Mostra SweetAlert
            Swal.fire({
                title: 'Sei sicuro?',
                text: "Stai per uscire senza salvare le modifiche.",
                icon: 'warning',
                showCancelButton: true,
                reverseButtons: true,
                customClass: {
                    cancelButton: "btn btn-light",
                    confirmButton: "btn btn-primary"
                },
                buttonsStyling: false,
                cancelButtonText: 'No, resta qui',
                confirmButtonText: 'Sì, esci senza salvare'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Se l'utente conferma, reindirizza all'href originale del pulsante
                    window.location.href = href;
                }
            });
        });
    }
});

// Preline UI Toast Notification Function using Toastify
function showToast(message, type = 'info') {
    const toastTypes = {
        success: {
            bgColor: 'bg-teal-500',
            icon: '<div class="shrink-0"><svg class="shrink-0 size-4 text-teal-500 mt-0.5" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z"></path></svg></div>'
        },
        error: {
            bgColor: 'bg-red-500',
            icon: '<div class="shrink-0"><svg class="shrink-0 size-4 text-red-500 mt-0.5" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM5.354 4.646a.5.5 0 1 0-.708.708L7.293 8l-2.647 2.646a.5.5 0 0 0 .708.708L8 8.707l2.646 2.647a.5.5 0 0 0 .708-.708L8.707 8l2.647-2.646a.5.5 0 0 0-.708-.708L8 7.293 5.354 4.646z"></path></svg></div>'
        },
        warning: {
            bgColor: 'bg-yellow-500',
            icon: '<div class="shrink-0"><svg class="shrink-0 size-4 text-yellow-500 mt-0.5" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM8 4a.905.905 0 0 0-.9.995l.35 3.507a.552.552 0 0 0 1.1 0l.35-3.507A.905.905 0 0 0 8 4zm.002 6a1 1 0 1 0 0 2 1 1 0 0 0 0-2z"></path></svg></div>'
        },
        info: {
            bgColor: 'bg-blue-500',
            icon: '<div class="shrink-0"><svg class="shrink-0 size-4 text-blue-500 mt-0.5" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm.93-9.412-1 4.705c-.07.34.029.533.304.533.194 0 .487-.07.686-.246l-.088.416c-.287.346-.92.598-1.465.598-.703 0-1.002-.422-.808-1.319l.738-3.468c.064-.293.006-.399-.287-.47l-.451-.081.082-.381 2.29-.287zM8 5.5a1 1 0 1 1 0-2 1 1 0 0 1 0 2z"></path></svg></div>'
        }
    };

    const toastConfig = toastTypes[type] || toastTypes.info;

    const toastHtml = `
            <div class="max-w-xs bg-white border border-gray-200 rounded-xl shadow-lg dark:bg-neutral-800 dark:border-neutral-700" role="alert" tabindex="-1" aria-labelledby="hs-toast-normal-example-label">
                <div class="flex p-4">
                    ${toastConfig.icon}
                    <div class="ms-3">
                        <p class="text-sm text-gray-700 dark:text-neutral-400">
                          ${message}
                        </p>
                    </div>
                    <button onclick="tostifyCustomClose(this)" type="button" class="inline-flex shrink-0 justify-center items-center size-5 rounded-lg text-gray-800 opacity-50 hover:opacity-100 focus:outline-hidden focus:opacity-100 dark:text-white" aria-label="Close">
                        <span class="sr-only">Close</span>
                        <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <path d="M18 6 6 18"></path>
                          <path d="m6 6 12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
        `;

    // Use Toastify to show the toast
    Toastify({
        text: toastHtml,
        className: "hs-toastify-on:opacity-100 opacity-0 fixed -top-10 end-10 z-90 transition-all duration-300 bg-white text-sm text-gray-700 rounded-xl shadow-lg [&>.toast-close]:hidden dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-400",
        duration: 4000,
        close: true,
        escapeMarkup: false,
        gravity: "top",
        position: "right"
    }).showToast();
}

function tostifyCustomClose(el) {
    const parent = el.closest('.toastify');
    const close = parent.querySelector('.toast-close');

    close.click();
}

// Dynamic Offcanvas Management System
// Creates and manages offcanvas modals with dynamic AJAX content loading
function createDynamicOffcanvas(options) {
    const defaults = {
        id: 'dynamic-offcanvas-' + Date.now(),
        title: 'Form',
        url: null,
        size: 'xl', // sm, md, lg, xl
        position: 'end', // start, end
        onContentLoaded: null,
        onClose: null,
        onSuccess: null,
        backdrop: true,
        keyboard: true
    };

    const config = Object.assign({}, defaults, options);

    // Validate required options
    if (!config.url) {
        console.error('createDynamicOffcanvas: URL is required');
        showToast('Errore: URL non specificato', 'error');
        return null;
    }

    // Check if required dependencies are available
    if (typeof HSOverlay === 'undefined') {
        console.error('createDynamicOffcanvas: HSOverlay not available');
        showToast('Errore: libreria UI non disponibile', 'error');
        return null;
    }

    if (typeof $ === 'undefined') {
        console.error('createDynamicOffcanvas: jQuery not available');
        showToast('Errore: jQuery non disponibile', 'error');
        return null;
    }

    // Create offcanvas HTML structure
    const offcanvasHtml = `
        <div id="${config.id}" class="hs-overlay hs-overlay-open:translate-x-0 hidden translate-x-full fixed top-0 ${config.position === 'start' ? 'start-0' : 'end-0'} transition-all duration-300 transform h-full ${
        config.size === 'sm' ? 'max-w-xs' :
            config.size === 'md' ? 'max-w-md' :
                config.size === 'lg' ? 'max-w-lg' :
                    config.size === 'xl' ? 'max-w-xl' :
                        'max-w-md'
    } w-full z-[80] bg-white border-s border-gray-200 dark:bg-neutral-800 dark:border-neutral-700" role="dialog" tabindex="-1" aria-labelledby="${config.id}-label">
            <div class="flex flex-col h-full">
                <div class="flex justify-between items-center py-3 px-4 border-b border-gray-200 dark:border-neutral-700">
                    <h3 id="${config.id}-label" class="font-bold text-gray-800 dark:text-white">
                        ${config.title}
                    </h3>
                    <button type="button" class="size-8 inline-flex justify-center items-center gap-x-2 rounded-full border border-transparent bg-gray-100 text-gray-800 hover:bg-gray-200 focus:outline-none focus:bg-gray-200 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-700 dark:hover:bg-neutral-600 dark:text-neutral-400 dark:focus:bg-neutral-600" aria-label="Close" data-hs-overlay="#${config.id}">
                        <span class="sr-only">Close</span>
                        <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 6 6 18"></path><path d="m6 6 12 12"></path></svg>
                    </button>
                </div>
                <div class="flex-1 overflow-hidden">
                    <div data-hs-tabs='{"isCollapsibleOnMobile": false}' class="h-full flex flex-col">
                        <!-- Tab Navigation -->
                        <nav class="inline-flex gap-1 p-0.5 w-full relative bg-gray-100 rounded-lg dark:bg-neutral-900/50" aria-label="Tabs" role="tablist" aria-orientation="horizontal">
                          <button type="button" class="hs-tab-active:bg-white hs-tab-active:shadow-sm hs-tab-active:focus:text-gray-800 py-2 px-3 w-full inline-flex justify-center items-center gap-x-2 text-sm text-gray-800 rounded-md disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden dark:text-neutral-200 dark:hs-tab-active:bg-neutral-700 dark:hs-tab-active:focus:text-neutral-200 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden active" id="${config.id}-tab-detail" data-hs-tab="#${config.id}-tab-detail-content" aria-controls="${config.id}-tab-detail-content" role="tab">
                            Dettaglio
                          </button>
                          <button type="button" class="hs-tab-active:bg-white hs-tab-active:shadow-sm hs-tab-active:focus:text-gray-800 py-2 px-3 w-full inline-flex justify-center items-center gap-x-2 text-sm text-gray-800 rounded-md disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden dark:text-neutral-200 dark:hs-tab-active:bg-neutral-700 dark:hs-tab-active:focus:text-neutral-200 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden" id="${config.id}-tab-activity" data-hs-tab="#${config.id}-tab-activity-content" aria-controls="${config.id}-tab-activity-content" role="tab">
                            Attività
                          </button>
                        </nav>

                        <!-- Tab Content -->
                        <div class="flex-1 overflow-hidden">
                            <!-- Detail Tab Content -->
                            <div id="${config.id}-tab-detail-content" class="h-full" role="tabpanel" aria-labelledby="${config.id}-tab-detail">
                                <div id="${config.id}-content" class="h-full">
                                    <div class="flex justify-center items-center h-32">
                                        <div class="animate-spin inline-block size-6 border-[3px] border-current border-t-transparent text-blue-600 rounded-full" role="status" aria-label="loading">
                                            <span class="sr-only">Loading...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Activity Tab Content -->
                            <div id="${config.id}-tab-activity-content" class="h-full hidden overflow-y-auto" role="tabpanel" aria-labelledby="${config.id}-tab-activity">
                                <div id="${config.id}-activity-content" class="p-4">
                                    <!-- Activity content will be loaded here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove any existing offcanvas with the same ID
    const existingOffcanvas = document.getElementById(config.id);
    if (existingOffcanvas) {
        existingOffcanvas.remove();
    }

    // Add offcanvas to DOM
    document.body.insertAdjacentHTML('beforeend', offcanvasHtml);
    const offcanvasElement = document.getElementById(config.id);

    // Load content via AJAX
    $.ajax({
        url: config.url,
        type: 'GET',
        success: function(response) {
            const contentContainer = document.getElementById(config.id + '-content');
            contentContainer.innerHTML = response;

            eval($(offcanvasElement).find(".reload-script-on-load").text().trim());

            // Initialize Preline components for the new content
            HSStaticMethods.autoInit();

            // Call content loaded callback
            if (typeof config.onContentLoaded === 'function') {
                config.onContentLoaded(offcanvasElement, contentContainer);
            }

            // Show the offcanvas
            const overlay = HSOverlay.getInstance(offcanvasElement, true);
            if (overlay) {
                overlay.element.open();
            }

            // Setup activity tab functionality
            setupActivityTab(config.id, config.url);
        },
        error: function(xhr, status, error) {
            const contentContainer = document.getElementById(config.id + '-content');
            contentContainer.innerHTML = `
                <div class="text-center">
                    <div class="mb-4">
                        <svg class="mx-auto size-12 text-red-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <line x1="15" x2="9" y1="9" y2="15"></line>
                            <line x1="9" x2="15" y1="9" y2="15"></line>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 dark:text-white">Errore di caricamento</h3>
                    <p class="text-sm text-gray-600 dark:text-neutral-400 mt-2">
                        Impossibile caricare il contenuto. Riprova più tardi.
                    </p>
                    <button type="button" class="mt-4 py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700" onclick="location.reload()">
                        Ricarica pagina
                    </button>
                </div>
            `;

            console.error('Error loading offcanvas content:', error);
            showToast('Errore durante il caricamento del contenuto', 'error');

            // Still show the offcanvas with error content
            const overlay = HSOverlay.getInstance(offcanvasElement, true);
            if (overlay) {
                overlay.element.open();
            }
        }
    });

    // Setup close event handler
    offcanvasElement.addEventListener('close.hs.overlay', function() {
        // Call close callback
        if (typeof config.onClose === 'function') {
            config.onClose(offcanvasElement);
        }

        // Clean up - remove from DOM after animation
        setTimeout(() => {
            if (offcanvasElement && offcanvasElement.parentNode) {
                offcanvasElement.remove();
            }
        }, 300);
    });

    // Return offcanvas management object
    return {
        element: offcanvasElement,
        id: config.id,
        close: function() {
            const overlay = HSOverlay.getInstance(offcanvasElement, true);
            if (overlay) {
                overlay.element.close();
            }
        },
        updateContent: function(newContent) {
            const contentContainer = document.getElementById(config.id + '-content');
            if (contentContainer) {
                contentContainer.innerHTML = newContent;
                HSStaticMethods.autoInit();
            }
        },
        onSuccess: function(callback) {
            config.onSuccess = callback;
        }
    };
}

/**
 * Setup activity tab functionality for dynamic offcanvas
 * @param {string} offcanvasId - The ID of the offcanvas
 * @param {string} formUrl - The original form URL to extract entity info
 */
function setupActivityTab(offcanvasId, formUrl) {
    const activityTab = document.getElementById(offcanvasId + '-tab-activity');
    if (!activityTab) return;

    let activityLoaded = false;

    // Add click handler for activity tab
    activityTab.addEventListener('click', function() {
        if (!activityLoaded) {
            loadActivityContent(offcanvasId, formUrl);
            activityLoaded = true;
        }
    });
}

/**
 * Load activity content for the entity
 * @param {string} offcanvasId - The ID of the offcanvas
 * @param {string} formUrl - The original form URL to extract entity info
 */
function loadActivityContent(offcanvasId, formUrl) {
    const activityContainer = document.getElementById(offcanvasId + '-activity-content');
    if (!activityContainer) return;

    // Show loading state
    activityContainer.innerHTML = `
        <div class="flex justify-center items-center h-32">
            <div class="animate-spin inline-block size-6 border-[3px] border-current border-t-transparent text-blue-600 rounded-full" role="status" aria-label="loading">
                <span class="sr-only">Loading...</span>
            </div>
        </div>
    `;

    // Extract entity information from form URL
    const entityInfo = extractEntityInfoFromUrl(formUrl);
    if (!entityInfo.entityType || !entityInfo.entityId) {
        activityContainer.innerHTML = `
            <div class="text-center py-8">
                <div class="mb-4">
                    <svg class="mx-auto size-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"/>
                        <line x1="15" x2="9" y1="9" y2="15"/>
                        <line x1="9" x2="15" y1="9" y2="15"/>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 dark:text-white">Informazioni non disponibili</h3>
                <p class="text-sm text-gray-600 dark:text-neutral-400 mt-2">Impossibile determinare il tipo di entità o l'ID.</p>
            </div>
        `;
        return;
    }

    // Load activity logs
    loadEntityLogs(entityInfo.entityType, entityInfo.entityId, 0, activityContainer);
}

/**
 * Extract entity type and ID from form URL
 * @param {string} url - The form URL
 * @returns {object} Object with entityType and entityId
 */
function extractEntityInfoFromUrl(url) {
    const result = { entityType: null, entityId: null };

    try {
        const urlObj = new URL(url, window.location.origin);
        const params = new URLSearchParams(urlObj.search);

        // Try to extract entity ID from various parameter names
        const idParams = ['userId', 'warrantyId', 'warrantyTypeId', 'channelId', 'insuranceCompanyId', 'insuranceProvenanceTypeId', 'warrantyDetailId'];
        for (const param of idParams) {
            const id = params.get(param);
            if (id) {
                result.entityId = id;
                // Extract entity type from parameter name (remove 'Id' suffix)
                result.entityType = param.replace(/Id$/, '').toLowerCase();
                break;
            }
        }

        // If no ID found in params, try to extract from URL path
        if (!result.entityId) {
            const pathParts = urlObj.pathname.split('/');
            const entityTypes = ['user', 'warranty', 'warrantytype', 'channel', 'insurancecompany', 'insuranceprovenancetype', 'warrantydetail'];

            for (let i = 0; i < pathParts.length; i++) {
                if (entityTypes.includes(pathParts[i])) {
                    result.entityType = pathParts[i];
                    // Look for ID in next part or in query params
                    if (i + 1 < pathParts.length && pathParts[i + 1]) {
                        result.entityId = pathParts[i + 1];
                    }
                    break;
                }
            }
        }

    } catch (e) {
        console.error('Error extracting entity info from URL:', e);
    }

    return result;
}