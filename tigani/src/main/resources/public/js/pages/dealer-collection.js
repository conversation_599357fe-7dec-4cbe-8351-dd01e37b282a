// Global variables for table management
const DealerCollection = function () {

    // Datatable
    const _componentDatatable = function () {
        // Initialize Preline UI DataTable with JavaScript configuration
        const datatableConfig = {
            paging: true,
            pageLength: 10,
            searching: true,
            ordering: true,
            info: true,
            lengthChange: true,
            scrollCollapse: true,
            ajax: {
                url: appRoutes.get("BE_DEALER_DATA"),
                type: 'GET'
            },
            select: {
                style: 'multi',
                selector: 'td:select-checkbox'
            },
            responsive: {
                details: {
                    type: 'column',
                    target: -1,
                }
            },
            columnDefs: [
                {
                    targets: 0,
                    width: '50px',
                    orderable: false,
                    className: 'select-checkbox',
                    render: function(data, type, row, meta) {
                        if (type === 'display') {
                            const rowId = row.id || meta.row;
                            return `
                                <div class="flex items-center h-5">
                                    <input id="hs-table-checkbox-${rowId}" type="checkbox" class="border-gray-300 rounded-sm text-blue-600 checked:border-blue-500 focus:ring-blue-500 dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-800" data-hs-datatable-row-selecting-individual="">
                                    <label for="hs-table-checkbox-${rowId}" class="sr-only">Checkbox</label>
                                </div>
                            `;
                        }
                        return data;
                    }
                },                
                {
                    targets: 4,                    
                    render: function(data, type, row, meta) {
                        if (type === 'display') {
                            const statusMap = {
                                'ACTIVE': '<span class="inline-flex items-center gap-x-1.5 py-1.5 px-3 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800/30 dark:text-green-500">Attivo</span>',
                                'INACTIVE': '<span class="inline-flex items-center gap-x-1.5 py-1.5 px-3 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-800/30 dark:text-red-500">Inattivo</span>',
                                'PENDING': '<span class="inline-flex items-center gap-x-1.5 py-1.5 px-3 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-800/30 dark:text-yellow-500">In Attesa</span>',
                                'premium': '<span class="inline-flex items-center gap-x-1.5 py-1.5 px-3 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-800/30 dark:text-blue-500">Premium</span>',
                                'standard': '<span class="inline-flex items-center gap-x-1.5 py-1.5 px-3 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-800/30 dark:text-purple-500">Standard</span>'
                            };
                            return statusMap[data] || `<span class="inline-flex items-center gap-x-1.5 py-1.5 px-3 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800/30 dark:text-gray-500">${data}</span>`;
                        }
                        return data;
                    }
                },
                {
                    targets: -1,
                    orderable: false,                    
                    render: function(data, type, row, meta) {
                        if (type === 'display') {
                            return _renderActionDropdown(row);
                        }
                        return data;
                    }
                }
            ],
            language: {                
                lengthMenu: 'Mostra _MENU_ elementi',
                paginate: {
                    first: 'Primo',
                    last: 'Ultimo',
                    next: 'Successivo',
                    previous: 'Precedente'
                },
                info: 'Mostra da _START_ a _END_ di _TOTAL_ elementi',
                infoEmpty: 'Mostra 0 a 0 di 0 elementi',
                infoFiltered: '(filtrati da _MAX_ elementi totali)',                
                emptyTable: '<div class=\"p-5 h-full flex flex-col justify-center items-center text-center\"><svg class=\"w-48 mx-auto mb-4\" width=\"178\" height=\"90\" viewBox=\"0 0 178 90\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><rect x=\"27\" y=\"50.5\" width=\"124\" height=\"39\" rx=\"7.5\" fill=\"currentColor\" class=\"fill-white dark:fill-neutral-800\"/><rect x=\"27\" y=\"50.5\" width=\"124\" height=\"39\" rx=\"7.5\" stroke=\"currentColor\" class=\"stroke-gray-50 dark:stroke-neutral-700/10\"/><rect x=\"34.5\" y=\"58\" width=\"24\" height=\"24\" rx=\"4\" fill=\"currentColor\" class=\"fill-gray-50 dark:fill-neutral-700/30\"/><rect x=\"66.5\" y=\"61\" width=\"60\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-50 dark:fill-neutral-700/30\"/><rect x=\"66.5\" y=\"73\" width=\"77\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-50 dark:fill-neutral-700/30\"/><rect x=\"19.5\" y=\"28.5\" width=\"139\" height=\"39\" rx=\"7.5\" fill=\"currentColor\" class=\"fill-white dark:fill-neutral-800\"/><rect x=\"19.5\" y=\"28.5\" width=\"139\" height=\"39\" rx=\"7.5\" stroke=\"currentColor\" class=\"stroke-gray-100 dark:stroke-neutral-700/30\"/><rect x=\"27\" y=\"36\" width=\"24\" height=\"24\" rx=\"4\" fill=\"currentColor\" class=\"fill-gray-100 dark:fill-neutral-700/70\"/><rect x=\"59\" y=\"39\" width=\"60\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-100 dark:fill-neutral-700/70\"/><rect x=\"59\" y=\"51\" width=\"92\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-100 dark:fill-neutral-700/70\"/><g filter=\"url(#@@id)\"><rect x=\"12\" y=\"6\" width=\"154\" height=\"40\" rx=\"8\" fill=\"currentColor\" class=\"fill-white dark:fill-neutral-800\" shape-rendering=\"crispEdges\"/><rect x=\"12.5\" y=\"6.5\" width=\"153\" height=\"39\" rx=\"7.5\" stroke=\"currentColor\" class=\"stroke-gray-100 dark:stroke-neutral-700/60\" shape-rendering=\"crispEdges\"/><rect x=\"20\" y=\"14\" width=\"24\" height=\"24\" rx=\"4\" fill=\"currentColor\" class=\"fill-gray-200 dark:fill-neutral-700 \"/><rect x=\"52\" y=\"17\" width=\"60\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-200 dark:fill-neutral-700\"/><rect x=\"52\" y=\"29\" width=\"106\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-200 dark:fill-neutral-700\"/></g><defs><filter id=\"@@id\" x=\"0\" y=\"0\" width=\"178\" height=\"64\" filterUnits=\"userSpaceOnUse\" color-interpolation-filters=\"sRGB\"><feFlood flood-opacity=\"0\" result=\"BackgroundImageFix\"/><feColorMatrix in=\"SourceAlpha\" type=\"matrix\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\" result=\"hardAlpha\"/><feOffset dy=\"6\"/><feGaussianBlur stdDeviation=\"6\"/><feComposite in2=\"hardAlpha\" operator=\"out\"/><feColorMatrix type=\"matrix\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0\"/><feBlend mode=\"normal\" in2=\"BackgroundImageFix\" result=\"effect1_dropShadow_1187_14810\"/><feBlend mode=\"normal\" in=\"SourceGraphic\" in2=\"effect1_dropShadow_1187_14810\" result=\"shape\"/></filter></defs></svg><div class=\"max-w-sm mx-auto\"><p class=\"mt-2 text-sm text-gray-600 dark:text-neutral-400\">Nessun dato disponibile</p></div></div>',
                zeroRecords: '<div class=\"p-5 h-full flex flex-col justify-center items-center text-center\"><svg class=\"w-48 mx-auto mb-4\" width=\"178\" height=\"90\" viewBox=\"0 0 178 90\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><rect x=\"27\" y=\"50.5\" width=\"124\" height=\"39\" rx=\"7.5\" fill=\"currentColor\" class=\"fill-white dark:fill-neutral-800\"/><rect x=\"27\" y=\"50.5\" width=\"124\" height=\"39\" rx=\"7.5\" stroke=\"currentColor\" class=\"stroke-gray-50 dark:stroke-neutral-700/10\"/><rect x=\"34.5\" y=\"58\" width=\"24\" height=\"24\" rx=\"4\" fill=\"currentColor\" class=\"fill-gray-50 dark:fill-neutral-700/30\"/><rect x=\"66.5\" y=\"61\" width=\"60\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-50 dark:fill-neutral-700/30\"/><rect x=\"66.5\" y=\"73\" width=\"77\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-50 dark:fill-neutral-700/30\"/><rect x=\"19.5\" y=\"28.5\" width=\"139\" height=\"39\" rx=\"7.5\" fill=\"currentColor\" class=\"fill-white dark:fill-neutral-800\"/><rect x=\"19.5\" y=\"28.5\" width=\"139\" height=\"39\" rx=\"7.5\" stroke=\"currentColor\" class=\"stroke-gray-100 dark:stroke-neutral-700/30\"/><rect x=\"27\" y=\"36\" width=\"24\" height=\"24\" rx=\"4\" fill=\"currentColor\" class=\"fill-gray-100 dark:fill-neutral-700/70\"/><rect x=\"59\" y=\"39\" width=\"60\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-100 dark:fill-neutral-700/70\"/><rect x=\"59\" y=\"51\" width=\"92\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-100 dark:fill-neutral-700/70\"/><g filter=\"url(#@@id)\"><rect x=\"12\" y=\"6\" width=\"154\" height=\"40\" rx=\"8\" fill=\"currentColor\" class=\"fill-white dark:fill-neutral-800\" shape-rendering=\"crispEdges\"/><rect x=\"12.5\" y=\"6.5\" width=\"153\" height=\"39\" rx=\"7.5\" stroke=\"currentColor\" class=\"stroke-gray-100 dark:stroke-neutral-700/60\" shape-rendering=\"crispEdges\"/><rect x=\"20\" y=\"14\" width=\"24\" height=\"24\" rx=\"4\" fill=\"currentColor\" class=\"fill-gray-200 dark:fill-neutral-700 \"/><rect x=\"52\" y=\"17\" width=\"60\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-200 dark:fill-neutral-700\"/><rect x=\"52\" y=\"29\" width=\"106\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-200 dark:fill-neutral-700\"/></g><defs><filter id=\"@@id\" x=\"0\" y=\"0\" width=\"178\" height=\"64\" filterUnits=\"userSpaceOnUse\" color-interpolation-filters=\"sRGB\"><feFlood flood-opacity=\"0\" result=\"BackgroundImageFix\"/><feColorMatrix in=\"SourceAlpha\" type=\"matrix\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\" result=\"hardAlpha\"/><feOffset dy=\"6\"/><feGaussianBlur stdDeviation=\"6\"/><feComposite in2=\"hardAlpha\" operator=\"out\"/><feColorMatrix type=\"matrix\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0\"/><feBlend mode=\"normal\" in2=\"BackgroundImageFix\" result=\"effect1_dropShadow_1187_14810\"/><feBlend mode=\"normal\" in=\"SourceGraphic\" in2=\"effect1_dropShadow_1187_14810\" result=\"shape\"/></filter></defs></svg><div class=\"max-w-sm mx-auto\"><p class=\"mt-2 text-sm text-gray-600 dark:text-neutral-400\">Nessun dato disponibile</p></div></div>',
                loadingRecords: '<div class=\"p-5 h-full flex flex-col justify-center items-center text-center\"><svg class=\"w-48 mx-auto mb-4\" width=\"178\" height=\"90\" viewBox=\"0 0 178 90\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><rect x=\"27\" y=\"50.5\" width=\"124\" height=\"39\" rx=\"7.5\" fill=\"currentColor\" class=\"fill-white dark:fill-neutral-800\"/><rect x=\"27\" y=\"50.5\" width=\"124\" height=\"39\" rx=\"7.5\" stroke=\"currentColor\" class=\"stroke-gray-50 dark:stroke-neutral-700/10\"/><rect x=\"34.5\" y=\"58\" width=\"24\" height=\"24\" rx=\"4\" fill=\"currentColor\" class=\"fill-gray-50 dark:fill-neutral-700/30\"/><rect x=\"66.5\" y=\"61\" width=\"60\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-50 dark:fill-neutral-700/30\"/><rect x=\"66.5\" y=\"73\" width=\"77\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-50 dark:fill-neutral-700/30\"/><rect x=\"19.5\" y=\"28.5\" width=\"139\" height=\"39\" rx=\"7.5\" fill=\"currentColor\" class=\"fill-white dark:fill-neutral-800\"/><rect x=\"19.5\" y=\"28.5\" width=\"139\" height=\"39\" rx=\"7.5\" stroke=\"currentColor\" class=\"stroke-gray-100 dark:stroke-neutral-700/30\"/><rect x=\"27\" y=\"36\" width=\"24\" height=\"24\" rx=\"4\" fill=\"currentColor\" class=\"fill-gray-100 dark:fill-neutral-700/70\"/><rect x=\"59\" y=\"39\" width=\"60\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-100 dark:fill-neutral-700/70\"/><rect x=\"59\" y=\"51\" width=\"92\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-100 dark:fill-neutral-700/70\"/><g filter=\"url(#@@id)\"><rect x=\"12\" y=\"6\" width=\"154\" height=\"40\" rx=\"8\" fill=\"currentColor\" class=\"fill-white dark:fill-neutral-800\" shape-rendering=\"crispEdges\"/><rect x=\"12.5\" y=\"6.5\" width=\"153\" height=\"39\" rx=\"7.5\" stroke=\"currentColor\" class=\"stroke-gray-100 dark:stroke-neutral-700/60\" shape-rendering=\"crispEdges\"/><rect x=\"20\" y=\"14\" width=\"24\" height=\"24\" rx=\"4\" fill=\"currentColor\" class=\"fill-gray-200 dark:fill-neutral-700 \"/><rect x=\"52\" y=\"17\" width=\"60\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-200 dark:fill-neutral-700\"/><rect x=\"52\" y=\"29\" width=\"106\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-200 dark:fill-neutral-700\"/></g><defs><filter id=\"@@id\" x=\"0\" y=\"0\" width=\"178\" height=\"64\" filterUnits=\"userSpaceOnUse\" color-interpolation-filters=\"sRGB\"><feFlood flood-opacity=\"0\" result=\"BackgroundImageFix\"/><feColorMatrix in=\"SourceAlpha\" type=\"matrix\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\" result=\"hardAlpha\"/><feOffset dy=\"6\"/><feGaussianBlur stdDeviation=\"6\"/><feComposite in2=\"hardAlpha\" operator=\"out\"/><feColorMatrix type=\"matrix\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0\"/><feBlend mode=\"normal\" in2=\"BackgroundImageFix\" result=\"effect1_dropShadow_1187_14810\"/><feBlend mode=\"normal\" in=\"SourceGraphic\" in2=\"effect1_dropShadow_1187_14810\" result=\"shape\"/></filter></defs></svg><div class=\"max-w-sm mx-auto\"><p class=\"mt-2 text-sm text-gray-600 dark:text-neutral-400\">Caricamento</p></div></div>',
                processing: '<div class=\"p-5 h-full flex flex-col justify-center items-center text-center\"><svg class=\"w-48 mx-auto mb-4\" width=\"178\" height=\"90\" viewBox=\"0 0 178 90\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><rect x=\"27\" y=\"50.5\" width=\"124\" height=\"39\" rx=\"7.5\" fill=\"currentColor\" class=\"fill-white dark:fill-neutral-800\"/><rect x=\"27\" y=\"50.5\" width=\"124\" height=\"39\" rx=\"7.5\" stroke=\"currentColor\" class=\"stroke-gray-50 dark:stroke-neutral-700/10\"/><rect x=\"34.5\" y=\"58\" width=\"24\" height=\"24\" rx=\"4\" fill=\"currentColor\" class=\"fill-gray-50 dark:fill-neutral-700/30\"/><rect x=\"66.5\" y=\"61\" width=\"60\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-50 dark:fill-neutral-700/30\"/><rect x=\"66.5\" y=\"73\" width=\"77\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-50 dark:fill-neutral-700/30\"/><rect x=\"19.5\" y=\"28.5\" width=\"139\" height=\"39\" rx=\"7.5\" fill=\"currentColor\" class=\"fill-white dark:fill-neutral-800\"/><rect x=\"19.5\" y=\"28.5\" width=\"139\" height=\"39\" rx=\"7.5\" stroke=\"currentColor\" class=\"stroke-gray-100 dark:stroke-neutral-700/30\"/><rect x=\"27\" y=\"36\" width=\"24\" height=\"24\" rx=\"4\" fill=\"currentColor\" class=\"fill-gray-100 dark:fill-neutral-700/70\"/><rect x=\"59\" y=\"39\" width=\"60\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-100 dark:fill-neutral-700/70\"/><rect x=\"59\" y=\"51\" width=\"92\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-100 dark:fill-neutral-700/70\"/><g filter=\"url(#@@id)\"><rect x=\"12\" y=\"6\" width=\"154\" height=\"40\" rx=\"8\" fill=\"currentColor\" class=\"fill-white dark:fill-neutral-800\" shape-rendering=\"crispEdges\"/><rect x=\"12.5\" y=\"6.5\" width=\"153\" height=\"39\" rx=\"7.5\" stroke=\"currentColor\" class=\"stroke-gray-100 dark:stroke-neutral-700/60\" shape-rendering=\"crispEdges\"/><rect x=\"20\" y=\"14\" width=\"24\" height=\"24\" rx=\"4\" fill=\"currentColor\" class=\"fill-gray-200 dark:fill-neutral-700 \"/><rect x=\"52\" y=\"17\" width=\"60\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-200 dark:fill-neutral-700\"/><rect x=\"52\" y=\"29\" width=\"106\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-200 dark:fill-neutral-700\"/></g><defs><filter id=\"@@id\" x=\"0\" y=\"0\" width=\"178\" height=\"64\" filterUnits=\"userSpaceOnUse\" color-interpolation-filters=\"sRGB\"><feFlood flood-opacity=\"0\" result=\"BackgroundImageFix\"/><feColorMatrix in=\"SourceAlpha\" type=\"matrix\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\" result=\"hardAlpha\"/><feOffset dy=\"6\"/><feGaussianBlur stdDeviation=\"6\"/><feComposite in2=\"hardAlpha\" operator=\"out\"/><feColorMatrix type=\"matrix\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0\"/><feBlend mode=\"normal\" in2=\"BackgroundImageFix\" result=\"effect1_dropShadow_1187_14810\"/><feBlend mode=\"normal\" in=\"SourceGraphic\" in2=\"effect1_dropShadow_1187_14810\" result=\"shape\"/></filter></defs></svg><div class=\"max-w-sm mx-auto\"><p class=\"mt-2 text-sm text-gray-600 dark:text-neutral-400\">Elaborazione</p></div></div>'
            },
            pagingOptions: {
                pageBtnClasses: 'min-w-10 flex justify-center items-center text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 py-2.5 text-sm rounded-full disabled:opacity-50 disabled:pointer-events-none dark:text-white dark:focus:bg-neutral-700 dark:hover:bg-neutral-700'
            },
            selecting: true,
            rowSelectingOptions: {
                selectAllSelector: '#hs-table-search-checkbox-all'
            },
            layout: {
                topStart: {
                    buttons: ["copy", "csv", "excel", "pdf", "print"]
                }
            }
        };

        // Initialize the DataTable
        window.dealersDataTable = HSDataTable.init('#dealer-datatable-container table', datatableConfig);
        
        // Store reference for global access
        if (window.dealersDataTable && window.dealersDataTable.dataTable) {
            // Add event listeners for row selection
            window.dealersDataTable.dataTable.on('select', function(e, dt, type, indexes) {
                _updateBulkActionVisibility();
            });

            window.dealersDataTable.dataTable.on('deselect', function(e, dt, type, indexes) {
                _updateBulkActionVisibility();
            });
        }
    };

    // Render action dropdown for each row
    const _renderActionDropdown = function(row) {
        const dealerId = row.id;
        const isArchived = row.archived || false;
        
        let actions = '';
        
        // View action
        if (hasPermission('DEALER_MANAGEMENT', 'view')) {
            actions += `
                <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700" href="#" onclick="DealerCollection.viewDealer('${dealerId}'); return false;">
                    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"/><circle cx="12" cy="12" r="3"/></svg>
                    Visualizza
                </a>
            `;
        }
        
        // Edit action
        if (hasPermission('DEALER_MANAGEMENT', 'edit')) {
            actions += `
                <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700" href="#" onclick="DealerCollection.editDealer('${dealerId}'); return false;">
                    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"/><path d="m15 5 4 4"/></svg>
                    Modifica
                </a>
            `;
        }
        
        // Archive/Unarchive action
        if (hasPermission('DEALER_MANAGEMENT', 'edit')) {
            if (isArchived) {
                actions += `
                    <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700" href="#" onclick="DealerCollection.unarchiveDealer('${dealerId}'); return false;">
                        <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="20" height="5" x="2" y="3" rx="1"/><path d="M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8"/><path d="M10 12h4"/></svg>
                        Ripristina
                    </a>
                `;
            } else {
                actions += `
                    <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700" href="#" onclick="DealerCollection.archiveDealer('${dealerId}'); return false;">
                        <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="20" height="5" x="2" y="3" rx="1"/><path d="M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8"/><path d="M10 12h4"/></svg>
                        Archivia
                    </a>
                `;
            }
        }
        
        // Delete action
        if (hasPermission('DEALER_MANAGEMENT', 'delete')) {
            actions += `
                <div class="hs-dropdown-divider"></div>
                <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-red-600 hover:bg-red-100 focus:outline-hidden focus:bg-red-100 dark:text-red-500 dark:hover:bg-red-800/30 dark:focus:bg-red-800/30" href="#" onclick="DealerCollection.deleteDealer('${dealerId}'); return false;">
                    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c-1 0 2 1 2 2v2"/><line x1="10" x2="10" y1="11" y2="17"/><line x1="14" x2="14" y1="11" y2="17"/></svg>
                    Elimina
                </a>
            `;
        }
        
        if (!actions) {
            return '<span class="text-gray-400 text-sm">Nessuna azione disponibile</span>';
        }
        
        return `
            <div class="hs-dropdown [--strategy:static] sm:[--strategy:fixed] [--adaptive:none] sm:[--adaptive:auto] relative inline-flex">
                <button id="hs-table-dropdown-${dealerId}" type="button" class="hs-dropdown-toggle py-1.5 px-2 inline-flex justify-center items-center gap-2 rounded-lg text-gray-700 align-middle disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-blue-600 transition-all text-sm dark:text-neutral-400 dark:hover:text-white dark:focus:ring-offset-gray-800" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="1"/><circle cx="19" cy="12" r="1"/><circle cx="5" cy="12" r="1"/></svg>
                </button>
                <div class="hs-dropdown-menu transition-[opacity,margin] duration hs-dropdown-open:opacity-100 opacity-0 hidden divide-y divide-gray-200 min-w-40 z-20 bg-white shadow-2xl rounded-lg p-2 mt-2 dark:divide-neutral-700 dark:bg-neutral-800 dark:border dark:border-neutral-700" role="menu" aria-orientation="vertical" aria-labelledby="hs-table-dropdown-${dealerId}">
                    ${actions}
                </div>
            </div>
        `;
    };

    // Update bulk action visibility based on selection
    const _updateBulkActionVisibility = function() {
        const selectedRows = document.querySelectorAll('[data-hs-datatable-row-selecting-individual]:checked');
        const bulkContainer = document.getElementById('bulk-actions-container');
        const selectedCount = document.getElementById('selected-count');

        if (selectedRows.length > 0) {
            bulkContainer.classList.remove('hidden');
            selectedCount.textContent = selectedRows.length;
        } else {
            bulkContainer.classList.add('hidden');
            selectedCount.textContent = '0';
        }
    };

    // Date range picker initialization
    function _componentDateRangePicker() {
        const dateRangeInput = $('#daterange');
        if (dateRangeInput.length === 0) return;

        const start = moment().subtract(29, 'days');
        const end = moment();

        function cb(start, end) {
            $('#daterange span').html(start.format('DD/MM/YYYY') + ' - ' + end.format('DD/MM/YYYY'));
        }

        dateRangeInput.daterangepicker({
            startDate: start,
            endDate: end,
            ranges: {
                'Oggi': [moment(), moment()],
                'Ieri': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                'Ultimi 7 Giorni': [moment().subtract(6, 'days'), moment()],
                'Ultimi 30 Giorni': [moment().subtract(29, 'days'), moment()],
                'Questo Mese': [moment().startOf('month'), moment().endOf('month')],
                'Mese Scorso': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
            },
            locale: {
                format: 'DD/MM/YYYY',
                applyLabel: 'Applica',
                cancelLabel: 'Annulla',
                startLabel: 'Data inizio',
                endLabel: 'Data fine',
                customRangeLabel: 'Personalizzato',
                daysOfWeek: ['Do', 'Lu', 'Ma', 'Me', 'Gi', 'Ve', 'Sa'],
                monthNames: ['Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno', 'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'],
                firstDay: 1
            }
        }, cb);

        cb(start, end);
    }

    function _componentAddDealer() {
        // Create Dealer Button Handler
        const createDealerBtn = document.getElementById('create-dealer-btn');
        if (createDealerBtn) {
            createDealerBtn.addEventListener('click', function() {
                try {
                    // Check if required functions are available
                    if (typeof createDynamicOffcanvas !== 'function') {
                        showToast('Errore: funzione offcanvas non disponibile', 'error');
                        return;
                    }

                    if (!appRoutes.has('BE_DEALER_FORM')) {
                        showToast('Errore: route non configurata', 'error');
                        return;
                    }

                    const offcanvas = createDynamicOffcanvas({
                        title: 'Nuovo Dealer',
                        url: appRoutes.get('BE_DEALER_FORM'),
                        onContentLoaded: function(offcanvasElement, contentContainer) {
                            try {
                                // Initialize dealer form components after content is loaded
                                if (typeof DealerForm !== 'undefined' && DealerForm.init) {
                                    DealerForm.init();
                                }
                            } catch (initError) {
                                console.error('Error initializing form:', initError);
                                showToast('Errore nell\'inizializzazione del modulo', 'error');
                            }
                        },
                        onClose: function() {
                            // Clean up any global variables if needed
                            if (typeof pond !== 'undefined' && pond && pond.destroy) {
                                try {
                                    pond.destroy();
                                } catch (e) {
                                    console.warn('Error destroying FilePond:', e);
                                }
                            }
                        }
                    });
                } catch (error) {
                    console.error('Error creating offcanvas:', error);
                    showToast('Errore nell\'apertura del modulo', 'error');
                }
            });
        } else {
            console.warn('Create dealer button not found');
        }
    }

    function _componentEditDealer() {
        // Edit Dealer Click Handler for table rows
        $(document).on('click', 'a[dealerId]', function(e) {
            e.preventDefault();

            try {
                const dealerId = $(this).attr('dealerId');
                const dealerName = $(this).text().trim();

                if (!dealerId) {
                    showToast('Errore: ID dealer non trovato', 'error');
                    return;
                }

                // Check if required functions are available
                if (typeof createDynamicOffcanvas !== 'function') {
                    showToast('Errore: funzione offcanvas non disponibile', 'error');
                    return;
                }

                if (!appRoutes.has('BE_DEALER_FORM')) {
                    showToast('Errore: route non configurata', 'error');
                    return;
                }

                const offcanvas = createDynamicOffcanvas({
                    title: 'Modifica Dealer: ' + (dealerName || 'Sconosciuto'),
                    url: appRoutes.get('BE_DEALER_FORM') + '?dealerId=' + encodeURIComponent(dealerId),
                    onContentLoaded: function(offcanvasElement, contentContainer) {
                        try {
                            // Initialize dealer form components after content is loaded
                            if (typeof DealerForm !== 'undefined' && DealerForm.init) {
                                DealerForm.init();
                            }
                        } catch (initError) {
                            console.error('Error initializing form:', initError);
                            showToast('Errore nell\'inizializzazione del modulo', 'error');
                        }
                    },
                    onClose: function() {
                        // Clean up any global variables if needed
                        if (typeof pond !== 'undefined' && pond && pond.destroy) {
                            try {
                                pond.destroy();
                            } catch (e) {
                                console.warn('Error destroying FilePond:', e);
                            }
                        }
                    }
                });
            } catch (error) {
                console.error('Error opening edit form:', error);
                showToast('Errore nell\'apertura del modulo di modifica', 'error');
            }
        });
    }

    // Setup manual checkbox handlers for Preline UI compatibility
    function _setupCheckboxHandlers() {
        // Handle select-all checkbox
        const selectAllCheckbox = document.getElementById('hs-table-search-checkbox-all');
        if (selectAllCheckbox) {
            selectAllCheckbox.removeEventListener('change', _handleSelectAll); // Remove existing listener
            selectAllCheckbox.addEventListener('change', _handleSelectAll);
        }

        // Handle individual row checkboxes
        const individualCheckboxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual]');
        individualCheckboxes.forEach(checkbox => {
            checkbox.removeEventListener('change', _handleIndividualSelect); // Remove existing listener
            checkbox.addEventListener('change', _handleIndividualSelect);
        });

        // Update bulk action buttons state
        _updateBulkActionButtons();
    }

    function _handleSelectAll(event) {
        const isChecked = event.target.checked;
        const individualCheckboxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual]');

        individualCheckboxes.forEach(checkbox => {
            checkbox.checked = isChecked;
        });

        _updateBulkActionButtons();
    }

    function _handleIndividualSelect() {
        const selectAllCheckbox = document.getElementById('hs-table-search-checkbox-all');
        const individualCheckboxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual]');
        const checkedBoxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual]:checked');

        if (selectAllCheckbox) {
            if (checkedBoxes.length === 0) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            } else if (checkedBoxes.length === individualCheckboxes.length) {
                selectAllCheckbox.checked = true;
                selectAllCheckbox.indeterminate = false;
            } else {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = true;
            }
        }

        _updateBulkActionButtons();
    }

    function _reloadTable(archived) {
        var newLink = appRoutes.get("BE_DEALER_DATA");

        // check archiviati
        var isArchivedChecked = archived || $("#dealer_archived:checked").length > 0;
        if (isArchivedChecked) {
            newLink += "?archived=" + isArchivedChecked;
        }

        if (window.dealersDataTable && window.dealersDataTable.dataTable) {
            window.dealersDataTable.dataTable.ajax.url(newLink).load();
        }
    }

    function _archiveSelectedRows() {
        // Check permission first
        if (!hasPermission('DEALER_MANAGEMENT', 'edit')) {
            showToast('Non hai i permessi per eseguire questa operazione.', 'error');
            return;
        }

        const selectedCheckboxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual]:checked');
        if (selectedCheckboxes.length === 0) {
            showToast('Seleziona almeno un dealer da archiviare.', 'warning');
            return;
        }

        const dealerIds = Array.from(selectedCheckboxes).map(checkbox => {
            const row = checkbox.closest('tr');
            return window.dealersDataTable.dataTable.row(row).data().id;
        });

        $.confirm({
            title: 'Conferma archiviazione',
            content: `Sei sicuro di voler archiviare ${dealerIds.length} dealer selezionati?`,
            type: 'orange',
            typeAnimated: true,
            buttons: {
                archivia: {
                    text: 'Archivia',
                    btnClass: 'btn-orange',
                    action: function () {
                        _performBulkOperation(dealerIds, 'archive');
                    }
                },
                annulla: {
                    text: 'Annulla',
                    btnClass: 'btn-light'
                }
            }
        });
    }

    function _confirmSelectedRows() {
        // Check permission first
        if (!hasPermission('DEALER_MANAGEMENT', 'edit')) {
            showToast('Non hai i permessi per eseguire questa operazione.', 'error');
            return;
        }

        const selectedCheckboxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual]:checked');
        if (selectedCheckboxes.length === 0) {
            showToast('Seleziona almeno un dealer da confermare.', 'warning');
            return;
        }

        const dealerIds = Array.from(selectedCheckboxes).map(checkbox => {
            const row = checkbox.closest('tr');
            return window.dealersDataTable.dataTable.row(row).data().id;
        });

        $.confirm({
            title: 'Conferma attivazione',
            content: `Sei sicuro di voler confermare ${dealerIds.length} dealer selezionati?`,
            type: 'green',
            typeAnimated: true,
            buttons: {
                conferma: {
                    text: 'Conferma',
                    btnClass: 'btn-green',
                    action: function () {
                        _performBulkOperation(dealerIds, 'confirm');
                    }
                },
                annulla: {
                    text: 'Annulla',
                    btnClass: 'btn-light'
                }
            }
        });
    }

    function _deleteSelectedRows() {
        // Check permission first
        if (!hasPermission('DEALER_MANAGEMENT', 'delete')) {
            showToast('Non hai i permessi per eseguire questa operazione.', 'error');
            return;
        }

        const selectedCheckboxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual]:checked');
        if (selectedCheckboxes.length === 0) {
            showToast('Seleziona almeno un dealer da eliminare.', 'warning');
            return;
        }

        const dealerIds = Array.from(selectedCheckboxes).map(checkbox => {
            const row = checkbox.closest('tr');
            return window.dealersDataTable.dataTable.row(row).data().id;
        });

        $.confirm({
            title: 'Conferma eliminazione',
            content: `Sei sicuro di voler eliminare definitivamente ${dealerIds.length} dealer selezionati? Questa azione non può essere annullata.`,
            type: 'red',
            typeAnimated: true,
            buttons: {
                elimina: {
                    text: 'Elimina',
                    btnClass: 'btn-red',
                    action: function () {
                        _performBulkOperation(dealerIds, 'delete');
                    }
                },
                annulla: {
                    text: 'Annulla',
                    btnClass: 'btn-light'
                }
            }
        });
    }

    function _performBulkOperation(dealerIds, operation) {
        const formData = new FormData();
        formData.append('dealerIds', dealerIds.join(','));
        formData.append('operation', operation);
        formData.append('fromArchived', $("#dealer_archived:checked").length > 0);

        $.ajax({
            url: appRoutes.get("BE_DEALER_OPERATE"),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function (response) {
                _reloadTable();
                _clearSelection();
                showToast('Operazione completata correttamente.', 'success');
            },
            error: function (error) {
                showToast(error.responseText || 'Errore durante l\'operazione.', 'error');
            }
        });
    }

    function _performSingleRowAction(dealerId, operation) {
        const formData = new FormData();
        formData.append('dealerIds', dealerId);
        formData.append('operation', operation);
        formData.append('fromArchived', $("#dealer_archived:checked").length > 0);

        $.ajax({
            url: appRoutes.get("BE_DEALER_OPERATE"),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function (response) {
                _reloadTable();
                showToast('Operazione completata correttamente.', 'success');
            },
            error: function (error) {
                showToast(error.responseText || 'Errore durante l\'operazione.', 'error');
            }
        });
    }

    function _clearSelection() {
        // Clear all checkboxes
        const allCheckboxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual], #hs-table-search-checkbox-all');
        allCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
            checkbox.indeterminate = false;
        });

        // Hide bulk actions
        const bulkContainer = document.getElementById('bulk-actions-container');
        if (bulkContainer) {
            bulkContainer.classList.add('hidden');
        }

        // Update counter
        const selectedCount = document.getElementById('selected-count');
        if (selectedCount) {
            selectedCount.textContent = '0';
        }
    }

    function _updateBulkActionButtons() {
        const selectedCheckboxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual]:checked');
        const bulkContainer = document.getElementById('bulk-actions-container');
        const selectedCount = document.getElementById('selected-count');

        if (selectedCheckboxes.length > 0) {
            bulkContainer.classList.remove('hidden');
            selectedCount.textContent = selectedCheckboxes.length;
        } else {
            bulkContainer.classList.add('hidden');
            selectedCount.textContent = '0';
        }
    }

    // Public methods for external access
    const publicMethods = {
        init: function() {
            _componentDatatable();
            _componentDateRangePicker();
            _componentAddDealer();
            _componentEditDealer();
            _setupCheckboxHandlers();
        },

        reloadTable: function(archived) {
            _reloadTable(archived);
        },

        clearSelection: function() {
            _clearSelection();
        },

        archiveSelectedRows: function() {
            _archiveSelectedRows();
        },

        confirmSelectedRows: function() {
            _confirmSelectedRows();
        },

        deleteSelectedRows: function() {
            _deleteSelectedRows();
        },

        viewDealer: function(dealerId) {
            // Implement view dealer functionality
            console.log('View dealer:', dealerId);
            // This could open a view modal or navigate to a view page
        },

        editDealer: function(dealerId) {
            try {
                if (typeof createDynamicOffcanvas !== 'function') {
                    showToast('Errore: funzione offcanvas non disponibile', 'error');
                    return;
                }

                if (!appRoutes.has('BE_DEALER_FORM')) {
                    showToast('Errore: route non configurata', 'error');
                    return;
                }

                const offcanvas = createDynamicOffcanvas({
                    title: 'Modifica Dealer',
                    url: appRoutes.get('BE_DEALER_FORM') + '?dealerId=' + encodeURIComponent(dealerId),
                    onContentLoaded: function(offcanvasElement, contentContainer) {
                        try {
                            if (typeof DealerForm !== 'undefined' && DealerForm.init) {
                                DealerForm.init();
                            }
                        } catch (initError) {
                            console.error('Error initializing form:', initError);
                            showToast('Errore nell\'inizializzazione del modulo', 'error');
                        }
                    },
                    onClose: function() {
                        if (typeof pond !== 'undefined' && pond && pond.destroy) {
                            try {
                                pond.destroy();
                            } catch (e) {
                                console.warn('Error destroying FilePond:', e);
                            }
                        }
                    }
                });
            } catch (error) {
                console.error('Error opening edit form:', error);
                showToast('Errore nell\'apertura del modulo di modifica', 'error');
            }
        },

        archiveDealer: function(dealerId) {
            $.confirm({
                title: 'Conferma archiviazione',
                content: 'Sei sicuro di voler archiviare questo dealer?',
                type: 'orange',
                typeAnimated: true,
                buttons: {
                    archivia: {
                        text: 'Archivia',
                        btnClass: 'btn-orange',
                        action: function () {
                            _performSingleRowAction(dealerId, 'archive');
                        }
                    },
                    annulla: {
                        text: 'Annulla',
                        btnClass: 'btn-light'
                    }
                }
            });
        },

        unarchiveDealer: function(dealerId) {
            $.confirm({
                title: 'Conferma ripristino',
                content: 'Sei sicuro di voler ripristinare questo dealer?',
                type: 'blue',
                typeAnimated: true,
                buttons: {
                    ripristina: {
                        text: 'Ripristina',
                        btnClass: 'btn-blue',
                        action: function () {
                            _performSingleRowAction(dealerId, 'unarchive');
                        }
                    },
                    annulla: {
                        text: 'Annulla',
                        btnClass: 'btn-light'
                    }
                }
            });
        },

        deleteDealer: function(dealerId) {
            $.confirm({
                title: 'Conferma eliminazione',
                content: 'Sei sicuro di voler eliminare definitivamente questo dealer? Questa azione non può essere annullata.',
                type: 'red',
                typeAnimated: true,
                buttons: {
                    elimina: {
                        text: 'Elimina',
                        btnClass: 'btn-red',
                        action: function () {
                            _performSingleRowAction(dealerId, 'delete');
                        }
                    },
                    annulla: {
                        text: 'Annulla',
                        btnClass: 'btn-light'
                    }
                }
            });
        }
    };

    return publicMethods;
}();

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    DealerCollection.init();
});

// Global table collection object for compatibility
window.TableCollection = {
    reloadTable: function(archived) {
        DealerCollection.reloadTable(archived);
    }
};
